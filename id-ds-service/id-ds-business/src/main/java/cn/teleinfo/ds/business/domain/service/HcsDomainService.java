package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;

import java.util.List;
import java.util.Map;

public interface HcsDomainService {
	/**
	 * 数据集成-规范层连接名称
	 */
	List<Links> findCdmConnections(SysConnection sysConnection, String projectId, String clusterId);


	/**
	 * 数据开发-规范层连接名称
	 */
	List<ApigDataSourceView> findDayuConnections(SysConnection sysConnection, String projectId, String workspace, Integer offset, Integer limit);

	/**
	 * 数据开发-规范层数据库名称
	 */
	List<DatabasesList> findDayuConnectionsDatabases(SysConnection sysConnection, String projectId, String workspace, String connectionId, Integer offset, Integer limit);

	/**
	 * 获取资源空间列表
	 */
	List<AuthProjectResult> findProjects(SysConnection sysConnection);

	/**
	 * 获取数据治理中心实例列表
	 */
	List<ApigCommodityOrder> findDasInstances(SysConnection sysConnection, String projectId, Integer current, Integer size);

	/**
	 * 获取工作空间列表
	 */
	List<Workspacebody> findDasWorkspaces(SysConnection sysConnection, String projectId, String instanceId, Integer current, Integer size);

	/**
	 * 获取CDM集群名称列表
	 */
	List<Clusters> findCdmClusters(SysConnection sysConnection, String projectId);

	/**
	 * 获取作业列表
	 */
	List<Job> findJobs(SysConnection sysConnection, String projectId, String clusterId);

	/**
	 * 创建脚本
	 */
	void createScript(SysConnection sysConnection, String projectId,String scriptName, String scriptContent, String workspace,String databaseName,String connectionName);

	/**
	 * 执行脚本
	 */
	String executeScript(SysConnection sysConnection,String projectId, String scriptName, String workspace);

	/**
	 * 查询脚本实例执行结果
	 */
	Map<String, String> listScriptResults(SysConnection sysConnection,String projectId, String scriptName, String workspace, String instanceId);
	/**
	 * 获取数据源中的表
	 */
	List<TablesList> listTables(SysConnection sysConnection, String projectId,String connectionId, String databaseName, String tableName);

	/**
	 * 获取表的字段
	 */
	List<ColumnsList> listTableColumns(SysConnection sysConnection,String projectId, String connectionId, String workSpace, String tableId);




}
