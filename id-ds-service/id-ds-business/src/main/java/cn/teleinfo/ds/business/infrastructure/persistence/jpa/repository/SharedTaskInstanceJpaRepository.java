package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskInstanceEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import feign.Param;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface SharedTaskInstanceJpaRepository extends BaseRepository<SharedTaskInstanceEntity, Long> {

	@Query(nativeQuery = true,
			value = """
					         SELECT
					             s.id,
					             s.shared_task_id AS sharedTaskId,
					             s.task_instance_no AS taskInstanceNo,
					             t.task_no as taskNo,
					             s.task_name as taskName,
					             s.task_type as taskType,
					             s.execution_type AS executionType,
					             s.run_status AS runStatus,
					              s.run_time AS runTime,
					              s.log_path as logPath
					FROM t_shared_task_instance s
					LEFT join t_share_task_applications t on t.id = s.shared_task_id
					where IF(:executionType IS NOT NULL, s.execution_type = :executionType, 1=1 )
					         and IF(:runStatus IS NOT NULL, s.run_status = :runStatus, 1=1 )
					         and IF(:sharedTaskId IS NOT NULL, s.shared_task_id = :sharedTaskId, 1=1 )
					         and IF(:taskInstanceNo != '' AND :taskInstanceNo is not null, s.task_instance_no like CONCAT('%',:taskInstanceNo,'%'), 1=1 )
					         and IF(:taskNo != '' AND :taskNo is not null, t.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
                    and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
                     AND IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code =:appHandleCode, 1=1 )
					             """,
			countQuery = """
				SELECT
				  count(*)
				   FROM t_shared_task_instance s
				   LEFT join t_share_task_applications t on t.id = s.shared_task_id
                    where IF(:executionType IS NOT NULL, s.execution_type = :executionType, 1=1 )
                    and IF(:runStatus IS NOT NULL, s.run_status = :runStatus, 1=1 )
                    and IF(:sharedTaskId IS NOT NULL, s.shared_task_id = :sharedTaskId, 1=1 )
                    and IF(:taskInstanceNo != '' AND :taskInstanceNo is not null, s.task_instance_no like CONCAT('%',:taskInstanceNo,'%'), 1=1 )
                    and IF(:taskNo != '' AND :taskNo is not null, t.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
                    and IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
                    AND IF(:appHandleCode != '' AND :appHandleCode is not null, s.app_handle_code =:appHandleCode, 1=1 )
                    """
	)
	Page<SharedTaskInstanceListView> listSharedTaskInstances(@Param("sharedTaskId") Long sharedTaskId, @Param("taskInstanceNo") String taskInstanceNo, @Param("executionType") Integer executionType, @Param("runStatus") Integer runStatus,@Param("taskNo") String taskNo, @Param("taskName") String taskName,@Param("appHandleCode")String appHandleCode, Pageable pageable);


	@Query(nativeQuery = true,
			value = """
					SELECT
					  s.id,
					  s.shared_task_id AS sharedTaskId,
					  s.task_instance_no AS taskInstanceNo,
					  s.task_name as taskName,
					  t.task_no as taskNo,
					  s.task_name as taskName,
					  s.task_type as taskType,
					  s.task_status as taskStatus,
					  s.execution_type AS executionType,
					  s.run_status AS runStatus,
					  s.target_source_id as targetSourceId,
					  s.run_duration as runDuration,
					  s.run_time as runTime,
					  s.log_path as logPath
					 from t_shared_task_instance s
					 left join t_share_task_applications t on t.id = s.shared_task_id
					 where s.id = :id
					  """
	)
	SharedTaskInstanceView getSharedTaskInstanceById(@Param("id")Long id);

	@Modifying
	@Query("UPDATE SharedTaskInstanceEntity " +
			"SET isDeleted = null " +
			"WHERE sharedTaskId = :sharedTaskId ")
    void deleteBySharedTaskId(@Param("sharedTaskId") Long sharedTaskId);
}