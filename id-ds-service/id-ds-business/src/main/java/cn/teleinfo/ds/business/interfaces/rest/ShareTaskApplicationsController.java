package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.command.ReviewCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.service.ShareTaskApplicationsAppService;
import cn.teleinfo.ds.business.application.service.ShareTaskAuthApplicationService;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.assembler.HandleItemDetailAssembler;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskApplicationsAssemble;
import cn.teleinfo.ds.business.interfaces.assembler.ShareTaskAuthApplicationAssemble;
import cn.teleinfo.ds.business.interfaces.dto.request.HandleItemDetailRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ListShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ReviewRequest;
import cn.teleinfo.ds.business.interfaces.dto.request.ShareTaskApplicationsRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemsResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.ShareTaskApplicationDetailResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 共享任务申请
 */
@RestController
@RequestMapping("/share-task-applications")
@AllArgsConstructor
public class ShareTaskApplicationsController {

	private final ShareTaskApplicationsAppService service;

	private final ShareTaskApplicationsAssemble assembler;

	private final ShareTaskAuthApplicationService shareTaskAuthApplicationService;
	private final ShareTaskAuthApplicationAssemble shareTaskAuthApplicationAssemble;
	private final HandleItemDetailAssembler handleItemDetailAssembler;

	/**
	 * 创建共享任务申请
	 */
	@PostMapping
	public R createShareTaskApplications(@RequestBody @Valid ShareTaskApplicationsRequest request) {
		var command = assembler.toCommand(request);
		service.createShareTaskApplications(command);
		return R.ok();
	}

	/**
	 * 修改共享任务申请
	 *
	 * @param request 修改参数
	 * @return 修改结果
	 */
	@PutMapping("{applicationId}")
	public R updateShareTaskApplications(@PathVariable Long applicationId, @RequestBody @Valid ShareTaskApplicationsRequest request) {
		var command = assembler.toCommand(request);
		service.updateShareTaskApplications(applicationId, command);
		return R.ok();
	}

	/**
	 * 查询共享任务列表
	 *
	 * @param request 查询参数
	 * @return 共享任务列表
	 */
	@GetMapping
	public R<PageResponse<ShareTaskApplicationsView>> listShareTaskApplications(@Valid ListShareTaskApplicationsRequest request) {
		var query = assembler.toQueryList(request);
		return R.ok(service.listShareTaskApplications(query));
	}

	/**
	 * 查询共享任务详情
	 *
	 * @param applicationId 共享任务ID
	 * @return 共享任务详情
	 */
	@GetMapping("/{applicationId}")
	public R<ShareTaskApplicationDetailResponse> getShareTaskApplicationDetail(@PathVariable Long applicationId) {
		ShareTaskApplicationDetails detail = service.getShareTaskApplicationDetail(applicationId);
		return R.ok(assembler.toDetailResponse(detail));
	}

	@PostMapping("/{shareTaskApplicationsId}/review")
	public R review(@PathVariable("shareTaskApplicationsId") Long shareTaskApplicationsId, @RequestBody @Valid ReviewRequest request) {
		ReviewCommand command = shareTaskAuthApplicationAssemble.toReviewCommand(shareTaskApplicationsId, request);
		shareTaskAuthApplicationService.review(command);
		return R.ok();
	}

	/**
	 * 根据handle获取handleItems
	 *
	 * @param handle handle
	 * @return handleItems
	 */
	@GetMapping("/handle-items")
	public R<HandleApplicationResponse> getHandleItemsByHandle(@Valid HandleItemDetailRequest handle) {
		HandleItemQuery query = handleItemDetailAssembler.toQuery(handle);
		return R.ok(service.getHandleItemsByHandle(query));
	}

	@DeleteMapping("/{shareTaskapplicationId}")
	public R deleteShareTaskApplications(@PathVariable Long shareTaskapplicationId) {
		service.deleteShareTaskApplications(shareTaskapplicationId);
		return R.ok();
	}
}

