package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareTaskApplicationsXqDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareTaskApplicationsEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

@Repository
public interface ShareTaskApplicationsJpaRepository extends BaseRepository<ShareTaskApplicationsEntity, Long> {

	@Query(nativeQuery = true, value = """
			SELECT
			    s.id,
			    s.task_name AS taskName,
			    s.task_no AS taskNo,
			    s.task_type AS taskType,
			    s.applications_status AS applicationsStatus,
			     s.update_time AS updatedTime,
			    u.`name` AS updateByName
			FROM t_share_task_applications s
			LEFT JOIN sys_user u ON s.update_by = u.user_id
			WHERE IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
			and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
			and IF(:taskType IS NOT NULL, s.task_type = :taskType, 1=1 )
			and IF(:applicationsStatus IS NOT NULL, s.applications_status = :applicationsStatus, 1=1 )
			AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.update_time BETWEEN :startTime AND :endTime , 1=1)
			AND IF(:appHandleCode IS NOT NULL, s.app_handle_code = :appHandleCode, 1=1)
			AND s.is_deleted = 0
			""", countQuery = """
			SELECT
				count(*)
				FROM t_share_task_applications s
				LEFT JOIN sys_user u ON s.update_by = u.user_id
					WHERE IF(:taskName != '' AND :taskName is not null, s.task_name like CONCAT('%',:taskName,'%'), 1=1 )
					and IF(:taskNo != '' AND :taskNo is not null, s.task_no like CONCAT('%',:taskNo,'%'), 1=1 )
					and IF(:taskType IS NOT NULL, s.task_type = :taskType, 1=1 )
					and IF(:applicationsStatus IS NOT NULL, s.applications_status = :applicationsStatus, 1=1 )
					AND IF(:startTime IS NOT NULL AND :endTime IS NOT NULL, s.update_time BETWEEN :startTime AND :endTime , 1=1)
					AND IF(:appHandleCode IS NOT NULL, s.app_handle_code = :appHandleCode, 1=1)
					AND s.is_deleted = 0
				""")
	Page<ShareTaskApplicationsView> findShareTaskApplications(@Param("taskName") String taskName, @Param("taskNo") String taskNo, @Param("taskType") Integer taskType, @Param("applicationsStatus") Integer applicationsStatus, @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime,@Param("appHandleCode") String appHandleCode, Pageable pageable);

	@Query(nativeQuery = true, value = """
			SELECT
			    s.id,
			    s.task_name AS taskName,
			    s.task_no AS taskNo,
			    s.task_type AS taskType,
			    s.target_source_id as targetSourceId,
			    s.applications_status AS applicationsStatus,
			     DATE_FORMAT(s.update_time, '%Y-%m-%d %H:%i:%s') AS updatedTime,
			    u.`name` AS updateByName,
			    S.app_handle_code as appHandleCode,
			    s.ent_prefix as entPrefix,
				s.graph as graph
			FROM t_share_task_applications s
			LEFT JOIN sys_user u ON s.update_by = u.user_id
			WHERE s.id = :id
			AND s.is_deleted = 0
			""")
	ShareTaskApplicationsXqDTO findShareTaskApplicationsById(@Param("id") Long id);

	@Modifying
	@Query("""
        UPDATE ShareTaskApplicationsEntity s
        SET s.applicationsStatus = :newStatus
        WHERE s.id = :id
        AND s.isDeleted = 0
    """)
	void updateStatusById(
			@Param("id") Long id,
			@Param("newStatus") Integer newStatus
	);
}