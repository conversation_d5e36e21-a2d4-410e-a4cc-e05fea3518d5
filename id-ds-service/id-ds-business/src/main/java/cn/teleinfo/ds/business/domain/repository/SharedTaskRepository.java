package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface SharedTaskRepository {

	Long save(SharedTaskDomainEntity sharedTaskDomainEntity);

	PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
														LocalDateTime start, LocalDateTime end, Integer page, Integer size);

	SharedTaskDomainEntity findById(Long id);

	void updateSharedTaskStatus(Long id, int taskStatus);

    void deleteById(Long id);
}
