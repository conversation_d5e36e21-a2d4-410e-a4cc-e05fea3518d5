package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.FieldSourceType;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesItem;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceItems;
import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.domain.repository.SharedTaskRepository;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.infrastructure.external.hcs.CreateJobTableDTO;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobResponse;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.SqlUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskDomainServiceImpl implements SharedTaskDomainService {
	private final SharedTaskRepository sharedTaskRepository;
	private final HcsRepository hcsRepository;


	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity, LocalDateTime start, LocalDateTime end, Integer page, Integer size) {
		return sharedTaskRepository.listSharedTask(entity, start, end, page, size);
	}

	@Override
	public SharedTaskDomainEntity getSharedTask(Long id) {
		return sharedTaskRepository.findById(id);
	}


	// 根据对象标识生成输出表 SQL
	@Override
	public String genOutputTablesSQL(SharedTask sharedTask) {
		List<HandleDomainEntity> handles = sharedTask.getHandles();
		StringBuilder allSQL = new StringBuilder();


		for (HandleDomainEntity handle : handles) {
			// 99.1000.1/YMZ12N35 => YMZ12N35
			String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1);

			masterTableName = "TSTD_" + masterTableName + "_BASIC";

			// 基础属性
			StringBuilder basicCreateSQL = new StringBuilder();
			String basicDropSQL = "-- 创建【" + handle.getName() + "】基础属性表\n" +
					"DROP TABLE IF EXISTS " + masterTableName + ";\n";

			basicCreateSQL.append("CREATE TABLE IF NOT EXISTS ").append(masterTableName).append(" (\n");

			// 基础属性
			List<HandleItemDomainEntity> basic = new ArrayList<>();

			// 扩展属性
			List<HandleItemDomainEntity> extend = new ArrayList<>();

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
					basic.add(item);
				}

				if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
					extend.add(item);
				}
			}

			if (basic.isEmpty()) {
				log.error("对象标识无基础属性 taskId={} handle={}", sharedTask.getSharedTask().getId(), handle.getHandle());
				throw new CheckedException("对象标识无基础属性");
			}

			for (int i = 0; i < basic.size(); i++) {
				var item = basic.get(i);

				var field = StrUtil.toUpperCase(item.getField());

				basicCreateSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");

				if (i < basic.size() - 1) {
					basicCreateSQL.append(",\n");
				} else {
					basicCreateSQL.append("\n");
				}
			}
			basicCreateSQL.append(") COMMENT '").append(handle.getName()).append("基础属性表';\n\n");

			var basicSql = basicCreateSQL.toString();
			sharedTask.getOutputTables().put(masterTableName, basicSql);
			allSQL.append(basicDropSQL).append(basicSql).append("\n");

			// 扩展属性 => 搞一个新表
			if (extend.isEmpty()) {
				log.warn("对象标识无扩展属性 taskId={} handle={}", sharedTask.getSharedTask().getId(), handle.getHandle());
				continue;
			}

			for (HandleItemDomainEntity item : extend) {
				var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + item.getField().toUpperCase();

				StringBuilder extendCreteSQL = new StringBuilder();
				String extendDropSQL = "-- 创建【" + handle.getName() + "】扩展属性表\n" +
						"DROP TABLE IF EXISTS " + extendTableName + ";\n";

				extendCreteSQL.append("CREATE TABLE IF NOT EXISTS ").append(extendTableName).append(" (\n");

				if (item.getShareChannel() == null) {
					throw new CheckedException("扩展属性无共享通道");
				}

				var sql = StrUtil.isEmpty(item.getShareChannel().getEditSql()) ? item.getShareChannel().getAutoSql() : item.getShareChannel().getEditSql();

				List<String> fields = SqlUtils.extractSelectSqlField(sql);
				if (fields == null) {
					log.warn("扩展属性共享通道 SQL 错误。 taskId={} handle={} SQL={}", sharedTask.getSharedTask().getId(), handle.getHandle(), sql);
					throw new CheckedException("扩展属性共享通道 SQL 错误");
				}

				for (int i = 0; i < fields.size(); i++) {
					String field = StrUtil.toUpperCase(fields.get(i));


					if (i == 0) {
						extendCreteSQL.append("    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					} else {
						extendCreteSQL.append(",\n    ").append(field).append(" STRING COMMENT '").append(field).append("'");
					}
				}

				extendCreteSQL.append("\n) COMMENT '").append(handle.getName()).append("扩展属性表-").append(item.getField()).append("';\n\n");

				var extendSql = extendCreteSQL.toString();
				sharedTask.getOutputTables().put(extendTableName, extendSql);
				allSQL.append(extendDropSQL).append(extendSql).append("\n");
			}

		}

		return allSQL.toString();
	}

	/**
	 * 生成提取 SQL
	 */
	@Override
	public String genExtractSQL(SharedTask sharedTask) {
		StringBuilder sqlBuilder = new StringBuilder();

		List<HandleDomainEntity> handles = sharedTask.getHandles();

		for (HandleDomainEntity handle : handles) {
			// 99.1000.1/YMZ12N35 => YMZ12N35
			String masterTableName = handle.getHandle().substring(handle.getHandle().lastIndexOf("/") + 1);
			masterTableName = "TSTD_" + masterTableName + "_BASIC";

			// 基础属性
			List<HandleItemDomainEntity> basic = new ArrayList<>();

			// 扩展属性
			List<HandleItemDomainEntity> extend = new ArrayList<>();

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				if (FieldSourceType.BASIC.code() == item.getFieldSourceType()) {
					basic.add(item);
				}

				if (FieldSourceType.EXTEND.code() == item.getFieldSourceType()) {
					extend.add(item);
				}
			}

			// 基础属性
			if (basic.isEmpty()) {
				log.error("对象标识无基础属性 taskId={} handle={}", sharedTask.getSharedTask().getId(), handle.getHandle());
				throw new CheckedException("对象标识无基础属性");
			}

			// 基础属性。对应同一个通道。
			HandleItemDomainEntity item = basic.get(0);
			var basicSQL = StrUtil.isEmpty(item.getShareChannel().getEditSql())
					? item.getShareChannel().getAutoSql() :
					item.getShareChannel().getEditSql();

			String basicCreteSQL = sharedTask.getOutputTables().get(masterTableName);

			var basicInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(basicCreteSQL, basicSQL);
			sqlBuilder.append("-- 生成基础属性表的数据抽取语句 ").append(masterTableName).append("\n");
			sqlBuilder.append(basicInsertSQL).append("\n");

			// 扩展属性
			for (HandleItemDomainEntity extendItem : extend) {
				var extendSQL = StrUtil.isEmpty(extendItem.getShareChannel().getEditSql())
						? extendItem.getShareChannel().getAutoSql() :
						extendItem.getShareChannel().getEditSql();

				var extendTableName = "TSTD_" + masterTableName + "_EXTEND_" + extendItem.getField().toUpperCase();

				String extendCreteSQL = sharedTask.getOutputTables().get(extendTableName);

				var extendInsertSQL = SqlUtils.genInsertSQLByCreateSQLAndSelectSQL(extendCreteSQL, extendSQL);

				sqlBuilder.append("-- 生成扩展属性表的数据抽取语句 ").append(extendTableName).append("\n");
				sqlBuilder.append(extendInsertSQL).append("\n");
			}
		}

		return StrUtil.replace(sqlBuilder.toString(), "INSERT INTO ", "INSERT OVERWRITE TABLE ");
	}

	/**
	 * 创建输出表结构
	 *
	 * @param sharedTask 共享任务
	 */
	@Override
	public void createOutputTables(SharedTask sharedTask, String SQL) {
		String scriptName = "outputSQL_" + sharedTask.getSharedTask().getTaskNo();
		execSQL(sharedTask, SQL, scriptName);
	}

	/**
	 * 写入输出表数据
	 */
	@Override
	public void writeOutputTables(SharedTask sharedTask, String extractSQL) {
		String scriptName = "extractSQL_" + sharedTask.getSharedTask().getTaskNo();
		execSQL(sharedTask, extractSQL, scriptName);
	}

	private void execSQL(SharedTask sharedTask, String SQL, String scriptName) {
		SysConnection sysConnection = new SysConnection(sharedTask.getSysConnection());
		String ak = sysConnection.getHcsConnContent().getAk();
		String sk = sysConnection.getHcsConnContent().getSk();
		List<String> endpoints = List.of(sysConnection.getHcsConnContent().getDgcEndpoint());

		// 共享源
		ShareDataSourcesDomainEntity shareDataSources = sharedTask.getAppInfo().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String workspace = shareDataSourcesItem.getWorkspace();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();

		ScriptInfo script = hcsRepository.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
		if (script != null) {
			hcsRepository.deleteScript(ak, sk, projectId, endpoints, scriptName, workspace);
		}

		// 创建脚本
		hcsRepository.createScript(ak, sk, projectId, endpoints, scriptName, SQL, workspace, stdDataConnName, stdDataDatabaseName);
		// 执行脚本
		String instanceId = hcsRepository.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
		// 查询脚本实例执行结果
		Map<String, String> results = hcsRepository.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);

		log.info("execSQL results={}", JSONUtil.toJsonPrettyStr(results));
	}

	/**
	 * 输出内容到指定数据源
	 */
	@Override
	public void outputToTargetDataSource(SharedTask sharedTask) {
		TargetSourceItems targetSourceItems = sharedTask.getTargetSource().getTargetSourceItems();

		SysConnection sysConnection = new SysConnection(sharedTask.getSysConnection());
		String ak = sysConnection.getHcsConnContent().getAk();
		String sk = sysConnection.getHcsConnContent().getSk();
		List<String> endpoints = List.of(sysConnection.getHcsConnContent().getDgcEndpoint());

		// 共享源
		ShareDataSourcesDomainEntity shareDataSources = sharedTask.getAppInfo().getShareDataSources();
		ShareDataSourcesItem shareDataSourcesItem = shareDataSources.getShareDataSourcesItem();
		String projectId = shareDataSourcesItem.getProjectId();
		String workspace = shareDataSourcesItem.getWorkspace();
		String stdDataConnName = shareDataSourcesItem.getStdDataConnName();
		String stdDataDatabaseName = shareDataSourcesItem.getStdDataDatabaseName();
		String clusterId = shareDataSourcesItem.getClusterId();

		List<CreateJobTableDTO> tables = new ArrayList<>();
		sharedTask.getOutputTables().forEach((k, v) -> {
			var table = new CreateJobTableDTO();
			table.setTableName(k);
			List<String> columnList = SqlUtils.extractSelectSqlField(v);
			table.setColumnList(columnList);

			tables.add(table);
		});

		// 创建作业
		CreateJobResponse response = hcsRepository.createJob(ak, sk, projectId, endpoints, clusterId, tables, stdDataConnName, stdDataDatabaseName,
				targetSourceItems.getDatabaseUrl(), targetSourceItems.getDatabaseName());

		// 启动作业
		hcsRepository.startJob(ak, sk, projectId, endpoints, clusterId, response.getName());
	}


	@Override
	public void updateSharedTaskStatus(Long id, int taskStatus) {
		sharedTaskRepository.updateSharedTaskStatus(id, taskStatus);
	}

	@Override
	public void deleteSharedTask(Long id) {
		sharedTaskRepository.deleteById(id);
	}
}
