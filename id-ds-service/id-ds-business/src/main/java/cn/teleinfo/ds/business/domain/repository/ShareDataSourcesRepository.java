package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.ShareSourceEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.time.LocalDateTime;


public interface ShareDataSourcesRepository {
	/**
	 * 创建共享数据源
	 *
	 * @param entity 共享数据源信息
	 */
	void createShareDataSources(ShareSourceEntity entity);

	/**
	 * 查询共享源列表
	 */
	PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start, LocalDateTime end, Integer current, Integer size);

	ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode);
}
