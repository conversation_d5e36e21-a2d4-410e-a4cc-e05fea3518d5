package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.domain.repository.SharedTaskDetailsRepository;
import cn.teleinfo.ds.business.domain.service.SharedTaskDetailDomainService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class SharedTaskDetailDomainServiceImpl implements SharedTaskDetailDomainService {

	private final SharedTaskDetailsRepository sharedTaskDetailsRepository;

	@Override
	public void deleteBySharedTaskId(Long id) {
		sharedTaskDetailsRepository.deleteBySharedTaskId(id);
	}

}
