package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SharedTaskDetailJpaRepository extends BaseRepository<SharedTaskDetailEntity, Long> {
	List<SharedTaskDetailEntity> findBySharedTaskId(Long id);

	@Modifying
	@Query("UPDATE SharedTaskDetailEntity " +
			"SET isDeleted = null " +
			"WHERE sharedTaskId = :sharedTaskId ")
    void deleteBySharedTaskId(@Param("sharedTaskId") Long sharedTaskId);
}