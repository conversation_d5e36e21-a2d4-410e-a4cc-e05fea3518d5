package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.domain.repository.HcsRepository;
import cn.teleinfo.ds.business.infrastructure.external.hcs.CreateJobTableDTO;
import cn.teleinfo.ds.business.infrastructure.external.hcs.HcsClient;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobResponse;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.dgc.v1.model.ShowScriptResponse;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@AllArgsConstructor
@Component
public class HcsRepositoryImpl implements HcsRepository {
	private final HcsClient client;

	/**
	 * 数据集成-规范层连接名称
	 */
	@Override
	public List<Links> findCdmConnections(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		return client.listConnections(ak, sk, projectId, endpoints, clusterId);
	}

	/**
	 * 数据开发-规范层连接名称
	 */
	@Override
	public List<ApigDataSourceView> findDayuConnections(String ak, String sk, String projectId,
														List<String> endpoints, String workspace, Integer offset, Integer limit) {
		return client.listDataConnections(ak, sk, projectId, endpoints, workspace, offset, limit);
	}

	/**
	 * 数据开发-规范层数据库名称
	 */
	@Override
	public List<DatabasesList> findDayuConnectionsDatabases(String ak, String sk, String projectId,
															List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit) {
		return client.listDataDatabases(ak, sk, projectId, endpoints, workspace, connectionId, offset, limit);
	}

	/**
	 * 获取资源空间列表
	 */
	@Override
	public List<AuthProjectResult> findProjects(String ak, String sk, List<String> endpoints) {
		return client.keystoneListAuthProjects(ak, sk, endpoints);
	}

	/**
	 * 获取数据治理中心实例列表
	 */
	@Override
	public List<ApigCommodityOrder> findDasInstances(String ak, String sk, String projectId,
													 List<String> endpoints, Integer offset, Integer limit) {
		return client.listDataArtsStudioInstances(ak, sk, projectId, endpoints, offset, limit);
	}

	/**
	 * 获取工作空间列表
	 */
	@Override
	public List<Workspacebody> findDasWorkspaces(String ak, String sk, String projectId, String instanceId,
												 List<String> endpoints, Integer offset, Integer limit) {
		return client.listManagerWorkSpacesRequest(ak, sk, projectId, instanceId, endpoints, offset, limit);
	}

	/**
	 * 获取CDM集群名称列表
	 */
	@Override
	public List<Clusters> findCdmClusters(String ak, String sk, String projectId, List<String> endpoints) {
		return client.listClusters(ak, sk, projectId, endpoints);
	}

	@Override
	public List<Job> findJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		return client.listJobs(ak, sk, projectId, endpoints, clusterId);
	}

	@Override
	public void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
							 String scriptContent, String workspace, String dataBaseName, String connectionName) {
		client.createScript(ak, sk, projectId, endpoints, scriptName, scriptContent, workspace, dataBaseName, connectionName);
	}

	/**
	 * 查询脚本
	 */
	@Override
	public ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName) {
		return client.findScript(ak, sk, projectId, endpoints, workspace, scriptName);
	}

	/**
	 * 删除脚本
	 */
	@Override
	public void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace) {
		client.deleteScript(ak, sk, projectId, endpoints, scriptName, workspace);
	}

	@Override
	public String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
								String workspace) {
		return client.executeScript(ak, sk, projectId, endpoints, scriptName, workspace);
	}

	@Override
	public Map<String, String> listScriptResults(String ak, String sk, String projectId, List<String> endpoints,
												 String scriptName, String workspace, String instanceId) {
		return client.listScriptResults(ak, sk, projectId, endpoints, scriptName, workspace, instanceId);
	}

	@Override
	public List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String databaseName, String tableName) {
		return client.listTables(ak, sk, projectId, endpoints, connectionId, databaseName, tableName);
	}

	@Override
	public List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace, String tableId) {
		return client.listTableColumns(ak, sk, projectId, endpoints, connectionId, workSpace, tableId);
	}

	/**
	 * 创建作业
	 *
	 * @param ak           ak
	 * @param sk           sk
	 * @param projectId    projectId
	 * @param endpoints    endpoints
	 * @param clusterId    集群 Id
	 * @param tables       迁移的表信息
	 * @param fromLinkName 源链接名称
	 * @param fromDatabase 源数据库
	 * @param toLinkName   目标链接名称
	 * @param toDatabase   目标数据库
	 */
	@Override
	public CreateJobResponse createJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, List<CreateJobTableDTO> tables, String fromLinkName, String fromDatabase, String toLinkName, String toDatabase) {
		return client.createJob(ak, sk, projectId, endpoints, clusterId, tables, fromLinkName, fromDatabase, toLinkName, toDatabase);
	}

	@Override
	public void startJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		client.startJob(ak, sk, projectId, endpoints, clusterId, jobName);
	}
}
