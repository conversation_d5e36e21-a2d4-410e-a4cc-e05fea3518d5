package cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository;

import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.BaseRepository;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface SharedTaskJpaRepository extends BaseRepository<SharedTaskEntity, Long> {

	@Modifying
	@Transactional
	@Query("UPDATE SharedTaskEntity SET taskStatus = :status WHERE id = :id")
	int updateTaskStatus(@Param("id") Long id, @Param("status") Integer status);
}