package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import com.pig4cloud.pig.common.core.util.PageResponse;
import net.sf.jsqlparser.JSQLParserException;

import java.time.LocalDateTime;

public interface SharedTaskDomainService {
	PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
														LocalDateTime start, LocalDateTime end, Integer page, Integer size);

	SharedTaskDomainEntity getSharedTask(Long id);

	String genOutputTablesSQL(SharedTask sharedTask);

	String genExtractSQL(SharedTask sharedTask) ;

	/**
	 * 创建输出表结构
	 */
	void createOutputTables(SharedTask sharedTask, String SQL);

	/**
	 * 写入输出表数据
	 */
	void writeOutputTables(SharedTask sharedTask,String extractSQL);

	/**
	 * 输出内容到指定数据源
	 */
	void outputToTargetDataSource(SharedTask sharedTask);

    void deleteSharedTask(Long id);


	void updateSharedTaskStatus(Long id, int taskStatus);
}
