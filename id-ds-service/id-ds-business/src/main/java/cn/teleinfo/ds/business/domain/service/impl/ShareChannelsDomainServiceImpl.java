package cn.teleinfo.ds.business.domain.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.teleinfo.ds.business.application.command.CreateShareChannelVersionCommand;
import cn.teleinfo.ds.business.application.command.UpdateShareChannelsApplicationsCommand;
import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsApplicationsDetail;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareChannelsVersion;
import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.*;
import cn.teleinfo.ds.business.domain.repository.*;
import cn.teleinfo.ds.business.domain.service.HcsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import cn.teleinfo.ds.business.domain.util.SqlGenerator;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionAuthDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import cn.teleinfo.ds.upms.api.feign.RoleService;
import cn.teleinfo.ds.upms.api.vo.RoleCommonVO;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.dataartsstudio.v1.model.ColumnsList;
import com.huaweicloud.sdk.dataartsstudio.v1.model.TablesList;
import com.pig4cloud.pig.common.core.constant.UserConstants;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import com.pig4cloud.pig.common.core.util.SqlUtils;
import com.pig4cloud.pig.common.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ShareChannelsDomainServiceImpl implements ShareChannelsDomainService {

	private final ShareChannelRepository shareChannelRepository;

	private final ShareChannelAuthRepository shareChannelAuthRepository;

	private final DataIntegratedRepository dataIntegratedRepository;

	private final ShareChannelApplicationsRepository shareChannelApplicationsRepository;

	private final Snowflake snowflake;

	private final RoleService roleService;

	private final ShareDataSourcesRepository shareDataSourcesRepository;

	private final SysConnectionDomainService sysConnectionDomainService;

	private final HcsDomainService hcsDomainService;
	private final ShareDataSourcesDomainService shareDataSourcesDomainService;

	@Override
	public PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query) {
		//当前登录人
		Long userId = SecurityUtils.getUser().getId();
		R<List<RoleCommonVO>> userList = roleService.getRoleListByUserId(userId);
		if (ObjectUtil.isNotNull(userList.getData())) {
			RoleCommonVO sysRole = userList.getData().get(0);

			if (!StrUtil.equals(UserConstants.USER_ADMIN_CODE, sysRole.getRoleCode())) {
				query.setUserAppHandle(sysRole.getAppHandleCode());
			}
		}


		return shareChannelRepository.listShareChannels(query, query.getCurrent(), query.getSize());
	}

	@Override
	public ShareChannelsDetailsDTO queryShareChannelsDetails(String id) {
		return shareChannelRepository.queryShareChannelsDetails(id);
	}

	@Override
	public List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId) {
		return shareChannelRepository.listShareChannelsVersion(shareChannelId);
	}

	@Override
	public ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String version) {
		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");
		return shareChannelRepository.queryVersionSql(shareChannelId, versions[0], versions[1]);
	}

	@Override
	public List<ShareChannelsVersion> shareChannelsVersion(String shareChannelId) {
		List<ShareChannelsVersionDTO> shareChannelsVersionDTOList = shareChannelRepository
				.queryShareChannelsVersionDTO(shareChannelId);
		List<ShareChannelsVersionAuthDTO> shareChannelsVersionDTOAuthList = shareChannelAuthRepository
				.queryShareChannelsVersionAuthDTO(shareChannelId);

		// 给审核记录分组
		Map<String, List<ShareChannelsVersionAuthDTO>> groupedByCompositeKey = shareChannelsVersionDTOAuthList.stream()
				.collect(Collectors
						.groupingBy(dto -> dto.getShareChannelId() + "_" + dto.getMainVersion() + "." + dto.getMinorVersion()));

		List<ShareChannelsVersion> shareChannelsVersions = new ArrayList<>();
		// 把共享通道和审核记录拼起来
		for (ShareChannelsVersionDTO shareChannelsVersionDTO : shareChannelsVersionDTOList) {
			// 此共享通道对应的审批记录
			List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList = groupedByCompositeKey
				.computeIfAbsent(shareChannelsVersionDTO.getShareChannelId() + "_" + shareChannelsVersionDTO.getMainVersion() + "."
						+ shareChannelsVersionDTO.getMinorVersion(), k -> new ArrayList<>());

			// 排序
			shareChannelsVersionAuthDTOList.sort((o1, o2) -> o2.getUpdatedTime().compareTo(o1.getUpdatedTime()));
			ShareChannelsVersion shareChannelsVersion = new ShareChannelsVersion(shareChannelsVersionDTO,
					shareChannelsVersionAuthDTOList);
			shareChannelsVersions.add(shareChannelsVersion);
		}

		return shareChannelsVersions;
	}

	@Override
	public PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelsQuery query) {
		return shareChannelRepository.listShareChannelsApplications(query, query.getCurrent(), query.getSize());
	}

	@Override
	public ShareChannelsApplicationsDetail queryShareChannelsApplicationDetails(String applicationId) {
		var shareChannelsApplicationDetails = shareChannelRepository
				.queryShareChannelsApplicationDetails(applicationId);

		List<ShareChannelsVersionAuthDTO> shareChannelsVersionAuthDTOList = shareChannelAuthRepository
				.queryShareChannelsVersionAuthDTO(shareChannelsApplicationDetails.getShareChannelId().toString(),
						shareChannelsApplicationDetails.getMainVersion().toString(),
						shareChannelsApplicationDetails.getMinorVersion().toString());
		shareChannelsVersionAuthDTOList.sort((o1, o2) -> o2.getUpdatedTime().compareTo(o1.getUpdatedTime()));

		return new ShareChannelsApplicationsDetail(shareChannelsApplicationDetails, shareChannelsVersionAuthDTOList);
	}

	@Override
	@Transactional
	public void createShareChannels() {
		// 1. 查询所有数据通道
		List<DataChannelDomainEntity> dataChannels = dataIntegratedRepository.dataChannelFindAll();
		if (dataChannels == null || dataChannels.isEmpty()) {
			return;
		}
		for (DataChannelDomainEntity dataChannel : dataChannels) {
			Long dataChannelId = dataChannel.getId() == null ? null : dataChannel.getId();
			if (dataChannelId == null)
				continue;
			// 2. 查询该数据通道下所有共享通道版本
			List<ShareChannel> shareChannels = shareChannelRepository.findShareChannelsByDataChannelId(dataChannelId);
			boolean needCreate = true;
			if (shareChannels != null && !shareChannels.isEmpty()) {
				// 存在关联的ShareChannel且enable!=1
				for (ShareChannel shareChannel : shareChannels) {
					if (shareChannel.getEnable() == null || shareChannel.getEnable() == 1) {
						needCreate = false;
						break;
					}
				}
			}
			if (needCreate) {
				String newSql;
				if (dataChannel.getObjectHandleType() == 1) {
					newSql = SqlUtils.baseToSql(dataChannel.getResolveSql());
				} else {
					newSql = SqlUtils.extendToSql(dataChannel.getResolveSql());
				}

				// 计算新版本号
				int mainVersion = 1;
				int minorVersion = 0;
				if (shareChannels != null && !shareChannels.isEmpty()) {
					String maxVersion = shareChannels.stream()
							.map(chanel -> chanel.getMainVersion() + "." + chanel.getMinorVersion())
							.max(String::compareTo)
							.orElse("1.0");
					String[] parts = maxVersion.split("\\.");
					try {
						mainVersion = Integer.parseInt(parts[0]);
						minorVersion = Integer.parseInt(parts[1]);
						if (minorVersion < 99) {
							minorVersion++;
						} else {
							mainVersion++;
							minorVersion = 0;
						}
					} catch (Exception e) {
						mainVersion = 1;
					}
				}
				ShareChannel shareChannel = new ShareChannel();
				BeanUtils.copyProperties(dataChannel, shareChannel);
				shareChannel.setId(snowflake.nextId());
				shareChannel.setShareChannelId(dataChannel.getId());
				shareChannel.setAutoSql(newSql);
				shareChannel.setMainVersion(mainVersion);
				shareChannel.setMinorVersion(minorVersion);
				shareChannel.setDataChannelId(dataChannel.getId());
				shareChannel.setEnable(0);
				shareChannel.setCreateTime(LocalDateTime.now());
				shareChannel.setUpdateTime(LocalDateTime.now());
				shareChannel.setChangeReason("自动生成");
				shareChannelRepository.saveShareChannel(shareChannel);

			}
		}
	}

	@Override
	public void createShareChannel(Long shareChannelId) {
		ShareChannel enableChannel = shareChannelRepository.findEnableChannel(shareChannelId);
		if (Objects.isNull(enableChannel)) {
			return;
		}
		DataChannelDomainEntity dataChannel = shareChannelRepository.findDataChannel(shareChannelId);
		if (Objects.isNull(dataChannel)) {
			return;
		}
		String newSql;
		String resolveSql = dataChannel.getResolveSql();
		if (dataChannel.getObjectHandleType() == 1) {
			newSql = SqlUtils.baseToSql(resolveSql);
		} else {
			newSql = SqlUtils.extendToSql(resolveSql);
		}
		if (newSql.equals(enableChannel.getAutoSql())) {
			// 企业节点数据通道暂未发生变更，无需生成！
			return;
		}
		ShareChannel maxVersion = shareChannelRepository.findMaxVersion(shareChannelId);
		Integer mainVersion = maxVersion.getMainVersion();
		Integer minorVersion = maxVersion.getMinorVersion();
		if (minorVersion < 99) {
			minorVersion++;
		} else {
			mainVersion++;
			minorVersion = 0;
		}

		ShareChannel shareChannel = new ShareChannel();
		BeanUtils.copyProperties(dataChannel, shareChannel);
		shareChannel.setId(snowflake.nextId());
		shareChannel.setShareChannelId(dataChannel.getId());
		shareChannel.setAutoSql(newSql);
		shareChannel.setMainVersion(mainVersion);
		shareChannel.setMinorVersion(minorVersion);
		shareChannel.setDataChannelId(dataChannel.getId());
		shareChannel.setEnable(0);
		shareChannel.setCreateTime(LocalDateTime.now());
		shareChannel.setUpdateTime(LocalDateTime.now());
		shareChannel.setChangeReason("自动生成");
		shareChannelRepository.saveShareChannel(shareChannel);
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void reviewShareChannels(String applicationId, UpdateShareChannelsApplicationsCommand command) {
		// 审核人
		Long userId = SecurityUtils.getUser().getId();
		// 当前时间
		Timestamp currentTime = new Timestamp(System.currentTimeMillis());

		ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity = shareChannelApplicationsRepository
				.queryById(applicationId);
		shareChannelApplicationsDomainEntity.setChannelStatus("reject".equals(command.getAction()) ? 2 : 3);
		shareChannelApplicationsDomainEntity.setAuditRemark(command.getComment());
		shareChannelApplicationsDomainEntity.setAuditUserId(userId);
		shareChannelApplicationsDomainEntity.setUpdatedTime(currentTime);
		shareChannelApplicationsRepository.save(shareChannelApplicationsDomainEntity);

		ShareChannelAuthDomainEntity shareChannelAuthDomainEntity = new ShareChannelAuthDomainEntity();
		shareChannelAuthDomainEntity.setId(snowflake.nextId());
		shareChannelAuthDomainEntity.setShareChannelId(shareChannelApplicationsDomainEntity.getShareChannelId());
		shareChannelAuthDomainEntity.setChannelStatus(shareChannelApplicationsDomainEntity.getChannelStatus());
		shareChannelAuthDomainEntity.setAuditRemark(shareChannelApplicationsDomainEntity.getAuditRemark());
		shareChannelAuthDomainEntity.setAuditUserId(userId);
		shareChannelAuthDomainEntity.setIsDeleted(0);
		shareChannelAuthDomainEntity.setCreatedTime(currentTime);
		shareChannelAuthDomainEntity.setUpdatedTime(currentTime);
		shareChannelAuthDomainEntity.setMainVersion(shareChannelApplicationsDomainEntity.getMainVersion());
		shareChannelAuthDomainEntity.setMinorVersion(shareChannelApplicationsDomainEntity.getMinorVersion());
		shareChannelAuthRepository.save(shareChannelAuthDomainEntity);

		// 如果审批通过 将当前版本生效
		if (shareChannelApplicationsDomainEntity.getChannelStatus() == 3) {
			shareChannelRepository.updateEnableByShareChannelIdAndVersion(
					shareChannelApplicationsDomainEntity.getShareChannelId(),
					shareChannelApplicationsDomainEntity.getMainVersion(),
					shareChannelApplicationsDomainEntity.getMinorVersion());
		}
	}

	@Override
	public List<String> queryChannelVersions(String shareChannelId) {
		return shareChannelRepository.queryChannelVersions(shareChannelId);
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void shareChannelVersionSave(CreateShareChannelVersionCommand command) {
		Long usrId = SecurityUtils.getUser().getId();
		Long shareChannelId = Long.parseLong(command.getShareChannelId());
		String version = command.getVersion();
		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");
		ShareChannel shareChannel = shareChannelRepository.findByShareChannelIdAndVersion(shareChannelId,
				Integer.parseInt(versions[0]), Integer.parseInt(versions[1]));

		ShareChannel maxVersion = shareChannelRepository.findMaxVersion(shareChannelId);
		Integer mainVersion = maxVersion.getMainVersion();
		Integer minorVersion = maxVersion.getMinorVersion();
		if (minorVersion < 99) {
			minorVersion++;
		}
		else {
			mainVersion++;
			minorVersion = 0;
		}

		ShareChannel newShareChannel = new ShareChannel();
		BeanUtils.copyProperties(shareChannel, newShareChannel);
		newShareChannel.setId(snowflake.nextId());
		newShareChannel.setEditSql(command.getEditSql());
		newShareChannel.setChangeReason(command.getChangeReason());
		newShareChannel.setMainVersion(mainVersion);
		newShareChannel.setMinorVersion(minorVersion);
		newShareChannel.setEnable(0);
		newShareChannel.setCreateBy(usrId);
		newShareChannel.setCreateTime(LocalDateTime.now());
		newShareChannel.setUpdateTime(null);
		newShareChannel.setUpdateBy(null);
		shareChannelRepository.saveShareChannel(newShareChannel);
	}

	@Override
	public ShareChannel findEnableChannel(Long id) {
		return shareChannelRepository.findEnableChannel(id);
	}

	@Override
	@Transactional(rollbackFor = RuntimeException.class)
	public void shareChannelVersionChange(String shareChannelId, String version) {
		// 当前登录人
		Long usrId = SecurityUtils.getUser().getId();
		// 当前时间
		Timestamp currentTime = new Timestamp(System.currentTimeMillis());

		if (version.startsWith("V")) {
			version = version.substring(1);
		}
		String[] versions = version.split("\\.");

		ShareChannel enableChannel = shareChannelRepository.findEnableChannel(Long.parseLong(shareChannelId));
		//如果没有生效版本
		if (Objects.isNull(enableChannel)) {
			// TODO 探测

			// 直接生效
			ShareChannel shareChannel = shareChannelRepository.findByShareChannelIdAndVersion(
					Long.parseLong(shareChannelId), Integer.parseInt(versions[0]), Integer.parseInt(versions[1]));
			shareChannel.setEnable(1);
			shareChannel.setUpdateTime(LocalDateTime.now());
			shareChannel.setUpdateBy(usrId);
			shareChannelRepository.saveShareChannel(shareChannel);
		}
		else {
			//查询是否有正在探测的通道
			List<ShareChannel> shareChannelList = shareChannelRepository.findByShareChannelIdAndStatus(
					Long.parseLong(shareChannelId), 1);
			if (!shareChannelList.isEmpty()) {
				// 有版本正在探测中，请稍后再试！
				return;
			}
			// TODO 探测

			// 发起审批
			ShareChannelApplicationsDomainEntity shareChannelApplicationsDomainEntity = shareChannelApplicationsRepository
				.findByShareChannelIdAndVersion(shareChannelId, Integer.parseInt(versions[0]),
						Integer.parseInt(versions[1]));
			if (Objects.isNull(shareChannelApplicationsDomainEntity)) {
				shareChannelApplicationsDomainEntity = new ShareChannelApplicationsDomainEntity();
				shareChannelApplicationsDomainEntity.setId(snowflake.nextId());
				shareChannelApplicationsDomainEntity.setShareChannelId(shareChannelId);
				shareChannelApplicationsDomainEntity.setIsDeleted(0);
				shareChannelApplicationsDomainEntity.setMainVersion(Integer.parseInt(versions[0]));
				shareChannelApplicationsDomainEntity.setMinorVersion(Integer.parseInt(versions[1]));
				shareChannelApplicationsDomainEntity.setCreatedTime(currentTime);
			}
			shareChannelApplicationsDomainEntity.setChannelStatus(1);
			shareChannelApplicationsDomainEntity.setUpdatedTime(currentTime);
			shareChannelApplicationsDomainEntity.setApplyUserId(usrId);
			shareChannelApplicationsDomainEntity.setAuditUserId(null);
			shareChannelApplicationsDomainEntity.setAuditRemark(null);
			shareChannelApplicationsRepository.save(shareChannelApplicationsDomainEntity);
		}
	}


	@Override
	public void detectShareChannels() {
		List<ShareChannel> allShareChannel = shareChannelRepository.findAllShareChannel();
		allShareChannel.forEach(shareChannel -> {
			String appHandleCode = shareChannel.getAppHandleCode();
			String editSql = shareChannel.getEditSql();
			String autoSql = shareChannel.getAutoSql();
			String sql = StringUtils.isEmpty(editSql) ? autoSql : editSql;
			Map<String, String> tyc = new HashMap<>();
			Map<String, String> stgColumnList = new HashMap<>();
			Map<String, String> stdFromColumnList = new HashMap<>();
			Map<String, String> stdToColumnList = new HashMap<>();
			Map<String, String> gfc = new HashMap<>();
			Map<String,List<ColumnsList>> stgColumnsType = new HashMap<>();
			Map<String,List<ColumnsList>> stdColumnsType = new HashMap<>();

			Set<String> tables;
			try {
				tables = SqlUtils.findTableName(sql);
			} catch (JSQLParserException e) {
				throw new RuntimeException(e);
			}
			Set<String> tycTable = new HashSet<>(tables);
			class BreakException extends RuntimeException {
			}

			ShareDataSourcesDomainEntity dataSourcesDomainEntity = shareDataSourcesRepository.findByAppHandleCode(appHandleCode);
			Integer platformType = dataSourcesDomainEntity.getPlatformType();
			SysConnectionDomainEntity sysConnectionDomainEntity = sysConnectionDomainService.findByPlatformType(platformType);
			String projectId = "";
			String clusterId = "";
			String connectionId = "";
			String connectionName = "";
			String workspace = "";
			//贴源层
			String stgDatabaseName = "";
			//规范层
			String stdDatabaseName = "";
			List<Job> jobs = hcsDomainService.findJobs(new SysConnection(sysConnectionDomainEntity), projectId, clusterId);
			try {
				// 从作业中获取贴源层的表
				jobs.stream()
						.flatMap(job -> job.getFromConfigValues().getConfigs().stream()
								.flatMap(config -> config.getInputs().stream()
										.filter(input -> "fromJobConfig.tableName".equals(input.getName()))
										.flatMap(input -> tycTable.stream()
												.filter(table -> input.getValue().toString().equals(table))
												.limit(1) // 每个table只匹配一次
												.flatMap(table -> job.getToConfigValues().getConfigs().stream()
														.flatMap(config1 -> config1.getInputs().stream()
																.filter(input1 -> "toJobConfig.tableName".equals(input1.getName()))
																.map(input1 -> {
																	tyc.put(table, input1.getValue().toString());
																	tycTable.remove(table);
																	// 查找同config下的columnList
																	String columnListValue = config1.getInputs().stream()
																			.filter(in -> "toJobConfig.columnList".equals(in.getName()))
																			.map(in -> in.getValue().toString())
																			.findFirst()
																			.orElse(null);
																	if (columnListValue != null) {
																		stgColumnList.put(table, columnListValue);
																	}
																	if (tycTable.isEmpty()) {
																		throw new BreakException();
																	}
																	return new AbstractMap.SimpleEntry<>(table, input1.getValue().toString());
																})
														)
												)
										)
								)
						).forEach(entry -> {
						}); // 结果已在map中处理，无需操作
			} catch (BreakException ignore) {
				// 正常中断
			}

			Set<String> gfcTable = new HashSet<>(tyc.values());

			try {
				//从作业中获取规范层的表
				jobs.stream()
						.flatMap(job -> job.getFromConfigValues().getConfigs().stream()
								.flatMap(config -> config.getInputs().stream()
										.filter(input -> "fromJobConfig.tableName".equals(input.getName()))
										.flatMap(input -> gfcTable.stream()
												.filter(table -> input.getValue().toString().equals(table))
												.limit(1) // 每个table只匹配一次
												.flatMap(table -> job.getToConfigValues().getConfigs().stream()
														.flatMap(config1 -> config1.getInputs().stream()
																.filter(input1 -> "toJobConfig.tableName".equals(input1.getName()))
																.map(input1 -> {
																	gfc.put(table, input1.getValue().toString());
																	gfcTable.remove(table);
																	// 查找同config1下的columnList
																	String columnListValue = config1.getInputs().stream()
																			.filter(in -> "toJobConfig.columnList".equals(in.getName()))
																			.map(in -> in.getValue().toString())
																			.findFirst()
																			.orElse(null);
																	if (columnListValue != null) {
																		stdToColumnList.put(table, columnListValue);
																	}
																	if (gfcTable.isEmpty()) {
																		throw new BreakException();
																	}
																	return new AbstractMap.SimpleEntry<>(table, input1.getValue().toString());
																})
														)
												)
										)
								)
						).forEach(entry -> {
						}); // 结果已在map中处理，无需操作
			} catch (BreakException ignore) {
				// 正常中断
			}
			// 获取贴源层表结构
			List<TablesList> stgtableList = hcsDomainService.listTables(new SysConnection(sysConnectionDomainEntity), projectId, connectionId, stgDatabaseName, "");
			Set<String> stgToMatch = new HashSet<>(tyc.values());
			for (TablesList tablesList : stgtableList) {
				String tableName = tablesList.getTableName();
				Iterator<String> it = stgToMatch.iterator();
				while (it.hasNext()) {
					String value = it.next();
					if (value.equals(tableName)) {
						String tableId = tablesList.getTableId();
						List<ColumnsList> columnsLists = hcsDomainService.listTableColumns(new SysConnection(sysConnectionDomainEntity), projectId, connectionId, stgDatabaseName, tableId);
						stgColumnsType.put(value, columnsLists);
						it.remove(); // 匹配后移除
						break; // 当前tablesList只匹配一个
					}
				}
				if (stgToMatch.isEmpty()) {
					break; // 全部匹配完提前结束
				}
			}
			//获取规范层表结构
			List<TablesList> stdTableList = hcsDomainService.listTables(new SysConnection(sysConnectionDomainEntity), projectId, connectionId, stgDatabaseName, "");
			Set<String> stdToMatch = new HashSet<>(gfc.values());
			for (TablesList tablesList : stdTableList) {
				String tableName = tablesList.getTableName();
				Iterator<String> it = stdToMatch.iterator();
				while (it.hasNext()) {
					String value = it.next();
					if (value.equals(tableName)) {
						String tableId = tablesList.getTableId();
						List<ColumnsList> columnsLists = hcsDomainService.listTableColumns(new SysConnection(sysConnectionDomainEntity), projectId, connectionId, stdDatabaseName, tableId);
						stdColumnsType.put(value, columnsLists);
						it.remove(); // 匹配后移除
						break; // 当前tablesList只匹配一个
					}
				}
				if (stgToMatch.isEmpty()) {
					break; // 全部匹配完提前结束
				}
			}
			record Script(String createScriptName,String createInstanceId){};
			Map<String,Script> createScriptMap = new HashMap<>();
			//使用脚本在规范层创建业务表
			for (String table : tables) {
				String createTableSql = SqlGenerator.generateCreateTableSql(table, stdColumnsType.get(table));
				String createScriptName = "DDL-"+table;
				hcsDomainService.createScript(new SysConnection(sysConnectionDomainEntity),projectId,createScriptName,createTableSql,workspace,stgDatabaseName,connectionName);
				String createInstanceId = hcsDomainService.executeScript(new SysConnection(sysConnectionDomainEntity), projectId, createScriptName, workspace);
				createScriptMap.put(table,new Script(createScriptName,createInstanceId));
			}
			Set<String> status = new HashSet<>(tables);
			while(!status.isEmpty()){
				for (String table : status) {
					Script script = createScriptMap.get(table);
					Map<String, String> results = hcsDomainService.listScriptResults(new SysConnection(sysConnectionDomainEntity), projectId, script.createScriptName(), workspace, script.createInstanceId());
					String s = results.get("status");
					if(s.equals("FINISHED")){
						status.remove(table);
					}
				}
			}
			//使用脚本将规范层的数据写入到规范层的业务表中
			Map<String,Script> insertScriptMap = new HashMap<>();
			for (String table : tables) {
				String insertSql = SqlGenerator.generateInsertSql(stdDatabaseName, gfc.get(table), table, stdToColumnList.get(table));
				String insertScriptName = "INSERT-"+table;
				hcsDomainService.createScript(new SysConnection(sysConnectionDomainEntity),projectId,insertScriptName,insertSql,workspace,stdDatabaseName,connectionName);
				String insertInstanceId = hcsDomainService.executeScript(new SysConnection(sysConnectionDomainEntity), projectId, insertScriptName, workspace);
				insertScriptMap.put(table,new Script(insertScriptName,insertInstanceId));
			}
			Set<String> status1 = new HashSet<>(tables);
			while(!status1.isEmpty()){
				for (String table : status1) {
					Script script = insertScriptMap.get(table);
					Map<String, String> results =hcsDomainService.listScriptResults(new SysConnection(sysConnectionDomainEntity), projectId, script.createScriptName(), workspace, script.createInstanceId());
					String s = results.get("status");
					if(s.equals("FINISHED")){
						status1.remove(table);
					}
				}
			}
		});
	}

}
