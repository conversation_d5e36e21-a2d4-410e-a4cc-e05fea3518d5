package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.teleinfo.ds.business.application.query.SharedTaskInstanceListQuery;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskInstanceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SharedTaskInstanceRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceListView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.SharedTaskInstanceView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskInstanceJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class SharedTaskInstanceRepositoryImpl implements SharedTaskInstanceRepository {

	private final SharedTaskInstanceJpaRepository repository;
	@Override
	public PageResponse<SharedTaskInstanceListView> listSharedTaskInstances(SharedTaskInstanceListQuery query, Integer page, Integer size) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);
		Page<SharedTaskInstanceListView> resultPage = repository.listSharedTaskInstances(
				query.getSharedTaskId(),
				query.getTaskInstanceNo(),
				query.getExecutionType(),
				query.getRunStatus(),
				query.getTaskNo(),
				query.getTaskName(),
				query.getAppHandleCode(),
				pageable);
		return new PageResponse<>(
				resultPage.getContent(),
				resultPage.getTotalElements(),
				(long) size,
				(long) page,
				(long) resultPage.getTotalPages());
	}

	@Override
	public SharedTaskInstanceView getSharedTaskInstanceById(Long instanceId) {
		return repository.getSharedTaskInstanceById(instanceId);
	}

	@Override
	public void deleteBySharedTaskId(Long id) {
		repository.deleteBySharedTaskId(id);
	}
}
