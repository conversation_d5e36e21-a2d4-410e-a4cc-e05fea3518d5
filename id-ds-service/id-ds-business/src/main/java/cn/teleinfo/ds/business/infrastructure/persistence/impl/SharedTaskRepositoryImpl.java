package cn.teleinfo.ds.business.infrastructure.persistence.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.repository.SharedTaskRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskDetailEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.entity.SharedTaskEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskDetailJpaRepository;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.repository.SharedTaskJpaRepository;
import com.pig4cloud.pig.common.core.util.PageResponse;
import jakarta.persistence.criteria.Predicate;
import lombok.AllArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Component
@AllArgsConstructor
public class SharedTaskRepositoryImpl implements SharedTaskRepository {
	private final SharedTaskJpaRepository sharedTaskJpaRepository;
	private final SharedTaskDetailJpaRepository sharedTaskDetailJpaRepository;

	@Override
	public Long save(SharedTaskDomainEntity sharedTaskDomainEntity) {
		var entity = BeanUtil.copyProperties(sharedTaskDomainEntity, SharedTaskEntity.class);
		this.sharedTaskJpaRepository.save(entity);
		return entity.getId();
	}

	@Override
	public PageResponse<SharedTaskDomainEntity> listSharedTask(SharedTaskDomainEntity entity,
															   LocalDateTime start, LocalDateTime end, Integer page, Integer size) {

		int pageIndex = (page > 0) ? page - 1 : 0;
		Pageable pageable = PageRequest.of(pageIndex, size);

		Page<SharedTaskEntity> p = sharedTaskJpaRepository.findAll((root, query, cb) -> {
			List<Predicate> predicates = new ArrayList<>();

			if (StringUtils.hasText(entity.getTaskName())) {
				predicates.add(cb.like(root.get("taskName"), "%" + entity.getTaskName() + "%"));
			}

			if (entity.getTaskType() != null) {
				predicates.add(cb.equal(root.get("taskType"), entity.getTaskType()));
			}

			if (entity.getRunStatus() != null) {
				predicates.add(cb.equal(root.get("runStatus"), entity.getRunStatus()));
			}

			// 最后一次运行时间
			if (start != null && end != null) {
				predicates.add(cb.between(root.get("lastRunTime"), start, end));
			} else if (start != null) {
				predicates.add(cb.greaterThanOrEqualTo(root.get("lastRunTime"), end));
			}


			return cb.and(predicates.toArray(new Predicate[0]));
		}, pageable);

		var records = p.stream().map(e -> BeanUtil.copyProperties(e, SharedTaskDomainEntity.class)).toList();

		return new PageResponse<>(records, p.getTotalElements(), (long) size, (long) page, (long) p.getTotalPages());
	}

	@Override
	public SharedTaskDomainEntity findById(Long id) {
		Optional<SharedTaskEntity> optional = sharedTaskJpaRepository.findById(id);

		List<SharedTaskDetailEntity> details = sharedTaskDetailJpaRepository.findBySharedTaskId(id);


		return optional.map(e -> {
			SharedTaskDomainEntity entity = BeanUtil.copyProperties(e, SharedTaskDomainEntity.class);
			entity.setDetails(BeanUtil.copyToList(details, SharedTaskDetail.class));
			return entity;
		}).orElse(null);
	}

	@Override
	public void updateSharedTaskStatus(Long id, int taskStatus) {
		sharedTaskJpaRepository.updateTaskStatus(id, taskStatus);
	}

	@Override
	public void deleteById(Long id) {
		sharedTaskJpaRepository.deleteById(id);
	}
}
