package cn.teleinfo.ds.business.interfaces.rest;

import cn.teleinfo.ds.business.application.query.ListSharedTaskQuery;
import cn.teleinfo.ds.business.application.service.SharedTaskApplicationService;
import cn.teleinfo.ds.business.interfaces.assembler.SharedTaskAssembler;
import cn.teleinfo.ds.business.interfaces.dto.request.ListSharedTaskRequest;
import cn.teleinfo.ds.business.interfaces.dto.response.ListSharedTaskResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.SharedTaskResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;
import com.pig4cloud.pig.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PutMapping;

/**
 * 共享任务
 */
@Slf4j
@RestController
@RequestMapping("/shared-tasks")
@AllArgsConstructor
public class SharedTaskController {

	private final SharedTaskAssembler sharedTaskAssembler;
	private final SharedTaskApplicationService sharedTaskApplicationService;

	/**
	 * 查询任务列表
	 *
	 * @return 任务列表
	 */
	@GetMapping
	public R<PageResponse<ListSharedTaskResponse>> listSharedTask(ListSharedTaskRequest request) {
		ListSharedTaskQuery query = sharedTaskAssembler.toListSharedTaskQuery(request);
		return R.ok(sharedTaskAssembler.toListSharedTaskResponse(sharedTaskApplicationService.listSharedTask(query)));
	}

	/**
	 * 查询任务详情
	 *
	 * @param id 任务ID
	 * @return 任务详情
	 */
	@GetMapping("{id}")
	public R<SharedTaskResponse> getSharedTask(@PathVariable("id") Long id) {
		return R.ok(sharedTaskAssembler.toSharedTaskResponse(sharedTaskApplicationService.getSharedTask(id)));
	}

	/**
	 * 共享任务启用停用
	 *
	 * @param id id
	 * @return
	 */
	@PutMapping("{id}")
	public R updateSharedTaskStatus(@PathVariable("id") Long id) {
		sharedTaskApplicationService.updateSharedTaskStatus(id);
		return R.ok();
	}

	/**
	 * 执行任务
	 *
	 * @param id 任务ID
	 * @return 结果
	 */
	//@Inner
	@PostMapping("/execute")
	public R execute(@RequestParam("id") Long id) {
		log.info("execute task id={}", id);


		// 1. 查询任务信息（task  task_details），任务信息包含目标源。 找到对象标识。
		// 2. 根据对象标识找到共享通道。
		// 3. 根据对象标识找到应用。然后找到共享源。

		// 4. 解析共享通道 sql 找到需要共享的表名。
		// 5. 调用华为接口，查询贴源层，规范层表名，用于还原表结构。（分析映射关系）
		// 6. 还原-原表到规范层

		// 7. 输出表。根据需要同步的对象标识生成输出表结构
		// 8. 输出到目标源

		sharedTaskApplicationService.execute(id);


		return R.ok();
	}

	/**
	 * 删除共享任务
	 *
	 * @param id 任务ID
	 * @return 删除结果
	 */
	@DeleteMapping("/{id}")
	public R deleteSharedTask(@PathVariable Long id) {
		log.info("delete shared task id={}", id);
		sharedTaskApplicationService.deleteSharedTaskId(id);
		return R.ok();
	}

}
