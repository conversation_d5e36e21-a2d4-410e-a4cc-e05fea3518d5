package cn.teleinfo.ds.business.domain.service.impl;

import cn.teleinfo.ds.business.application.query.ListAppInfoQuery;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.repository.AppInfoRepository;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.HandleSignAppInfoDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class AppInfoDomainServiceImpl implements AppInfoDomainService {

	private final AppInfoRepository appInfoRepository;

	@Override
	public PageResponse<HandleSignAppInfoDTO> listHandleSignAppInfo(ListAppInfoQuery query) {
		return appInfoRepository.listHandleSignAppInfo(query, query.getCurrent(), query.getSize());
	}

	@Override
	public HandleSignAppInfoDTO queryHandleSignAppInfoDetail(String id) {
		return appInfoRepository.queryHandleSignAppInfoDetail(id);
	}

	@Override
	public AppInfoDomainEntity findByHandleCode(String appHandleCode) {
		return appInfoRepository.findByHandleCode(appHandleCode);
	}
}
