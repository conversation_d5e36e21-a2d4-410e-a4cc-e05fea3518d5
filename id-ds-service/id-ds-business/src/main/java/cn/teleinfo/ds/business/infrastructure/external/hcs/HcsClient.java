package cn.teleinfo.ds.business.infrastructure.external.hcs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.huaweicloud.sdk.cdm.v1.CdmClient;
import com.huaweicloud.sdk.cdm.v1.model.CdmCreateJobJsonReq;
import com.huaweicloud.sdk.cdm.v1.model.Clusters;
import com.huaweicloud.sdk.cdm.v1.model.ConfigValues;
import com.huaweicloud.sdk.cdm.v1.model.Configs;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobRequest;
import com.huaweicloud.sdk.cdm.v1.model.CreateJobResponse;
import com.huaweicloud.sdk.cdm.v1.model.Input;
import com.huaweicloud.sdk.cdm.v1.model.Job;
import com.huaweicloud.sdk.cdm.v1.model.Links;
import com.huaweicloud.sdk.cdm.v1.model.ListClustersRequest;
import com.huaweicloud.sdk.cdm.v1.model.ListClustersResponse;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobsRequest;
import com.huaweicloud.sdk.cdm.v1.model.ShowJobsResponse;
import com.huaweicloud.sdk.cdm.v1.model.ShowLinkRequest;
import com.huaweicloud.sdk.cdm.v1.model.ShowLinkResponse;
import com.huaweicloud.sdk.cdm.v1.model.StartJobRequest;
import com.huaweicloud.sdk.cdm.v1.model.StartJobResponse;
import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.http.HttpConfig;
import com.huaweicloud.sdk.dataartsstudio.v1.DataArtsStudioClient;
import com.huaweicloud.sdk.dataartsstudio.v1.model.*;
import com.huaweicloud.sdk.dgc.v1.DgcClient;
import com.huaweicloud.sdk.dgc.v1.model.CreateScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.CreateScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.DeleteScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.DeleteScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptReq;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.ExecuteScriptResponse;
import com.huaweicloud.sdk.dgc.v1.model.ListJobsRequest;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptResultsRequest;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptResultsResponse;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptsRequest;
import com.huaweicloud.sdk.dgc.v1.model.ListScriptsResponse;
import com.huaweicloud.sdk.dgc.v1.model.ScriptInfo;
import com.huaweicloud.sdk.dgc.v1.model.ShowScriptRequest;
import com.huaweicloud.sdk.dgc.v1.model.ShowScriptResponse;
import com.huaweicloud.sdk.iam.v3.IamClient;
import com.huaweicloud.sdk.iam.v3.model.AuthProjectResult;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsRequest;
import com.huaweicloud.sdk.iam.v3.model.KeystoneListAuthProjectsResponse;
import com.pig4cloud.pig.common.core.exception.CheckedException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 与华为云对接
 */
@Slf4j
@Component
public class HcsClient {

	private IamClient buildIamClient(String ak, String sk, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建IAM客户端
		return IamClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	private DataArtsStudioClient buildDataArtsStudioClient(String ak, String sk, String projectId,
														   List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建DataArtsStudioClient客户端
		return DataArtsStudioClient.newBuilder()
				.withCredential(auth)
				.withHttpConfig(httpConfig)
				.withEndpoints(endpoints)
				.build();
	}

	private CdmClient buildCdmClient(String ak, String sk, String projectId, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建CDM客户端
		return CdmClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	private DgcClient buildDgcClient(String ak, String sk, String projectId, List<String> endpoints) {
		// 认证凭证
		var auth = new BasicCredentials().withProjectId(projectId).withAk(ak).withSk(sk);
		// 禁用SSL校验
		var httpConfig = HttpConfig.getDefaultHttpConfig();
		httpConfig.setIgnoreSSLVerification(true);
		// 构建Dgc客户端
		return DgcClient.newBuilder()
				.withHttpConfig(httpConfig)
				.withCredential(auth)
				.withEndpoints(endpoints)
				.build();
	}

	// 获取资源空间列表
	public List<AuthProjectResult> keystoneListAuthProjects(String ak, String sk, List<String> endpoints) {
		KeystoneListAuthProjectsRequest request = new KeystoneListAuthProjectsRequest();
		KeystoneListAuthProjectsResponse response = this.buildIamClient(ak, sk, endpoints)
				.keystoneListAuthProjects(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取资源空间列表列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取资源空间列表列表错误");
		}

		return response.getProjects();
	}

	// 获取数据治理中心实例列表
	public List<ApigCommodityOrder> listDataArtsStudioInstances(String ak, String sk, String projectId,
																List<String> endpoints, Integer offset, Integer limit) {
		ListDataArtsStudioInstancesRequest request = new ListDataArtsStudioInstancesRequest();
		request.withLimit(limit);
		request.withOffset(offset);
		ListDataArtsStudioInstancesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDataArtsStudioInstances(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据治理中心实例列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据治理中心实例列表错误");
		}

		return response.getCommodityOrderLists();
	}

	// 获取工作空间列表
	public List<Workspacebody> listManagerWorkSpacesRequest(String ak, String sk, String projectId, String instanceId,
															List<String> endpoints, Integer offset, Integer limit) {
		ListManagerWorkSpacesRequest request = new ListManagerWorkSpacesRequest();
		request.withInstanceId(instanceId);
		request.withLimit(limit);
		request.withOffset(offset);
		ListManagerWorkSpacesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listManagerWorkSpaces(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取工作空间列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取工作空间列表错误");
		}

		return response.getData();
	}

	// 获取CDM集群名称列表
	public List<Clusters> listClusters(String ak, String sk, String projectId, List<String> endpoints) {
		ListClustersRequest request = new ListClustersRequest();
		ListClustersResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).listClusters(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取CDM集群名称列表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取CDM集群名称列表错误");
		}

		return response.getClusters();
	}

	// 数据集成-规范层连接名称
	public List<Links> listConnections(String ak, String sk, String projectId, List<String> endpoints,
									   String clusterId) {
		ShowLinkRequest request = new ShowLinkRequest();
		request.setClusterId(clusterId);
		request.setLinkName("all");
		ShowLinkResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showLink(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据集成-规范层连接名称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据集成-规范层连接名称错误");
		}

		return response.getLinks();
	}

	// 数据开发-规范层连接名称
	public List<ApigDataSourceView> listDataConnections(String ak, String sk, String projectId,
														List<String> endpoints, String workspace, Integer offset, Integer limit) {
		ListDataconnectionsRequest request = new ListDataconnectionsRequest();
		request.withWorkspace(workspace);
		request.withLimit(String.valueOf(limit));
		request.withOffset(String.valueOf(offset));
		ListDataconnectionsResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDataconnections(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据开发-规范层连接名称称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据开发-规范层连接名称称错误");
		}

		return response.getDataConnectionLists();
	}

	// 数据开发-规范层数据库名称
	public List<DatabasesList> listDataDatabases(String ak, String sk, String projectId,
												 List<String> endpoints, String workspace, String connectionId, Integer offset, Integer limit) {
		ListDatabasesRequest request = new ListDatabasesRequest();
		request.withWorkspace(workspace);
		request.withConnectionId(connectionId);
		request.withLimit(String.valueOf(limit));
		request.withOffset(String.valueOf(offset));
		ListDatabasesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints)
				.listDatabases(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据开发-规范层数据库名称错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据开发-规范层数据库名称错误");
		}

		return response.getDatabases();
	}

	// 获取所有作业
	public List<Job> listJobs(String ak, String sk, String projectId, List<String> endpoints, String clusterId) {
		ShowJobsRequest request = new ShowJobsRequest();
		request.setClusterId(clusterId);
		request.setJobName("all");
		ShowJobsResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).showJobs(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取所有作业错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取所有作业错误");
		}
		return response.getJobs();
	}

	// 创建脚本
	public void createScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
							 String scriptContent, String workspace, String databaseName, String connectionName) {

		CreateScriptRequest request = new CreateScriptRequest();
		request.withWorkspace(workspace);
		ScriptInfo body = new ScriptInfo();
		body.withContent(scriptContent);
		body.withDirectory("/Detection");
		body.withType(ScriptInfo.TypeEnum.fromValue("HiveSQL"));
		body.withName(scriptName);
		body.withConnectionName(connectionName);
		body.withDatabase(databaseName);
		request.withBody(body);
		CreateScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).createScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("创建脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("创建脚本错误");
		}
	}

	// 查询脚本
	public ScriptInfo findScript(String ak, String sk, String projectId, List<String> endpoints, String workspace, String scriptName) {
		ListScriptsRequest request = new ListScriptsRequest();
		request.withWorkspace(workspace);
		request.withLimit(20);
		request.setScriptName(scriptName);
		request.withOffset(0);

		ListScriptsResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).listScripts(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("查询脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			return null;
		}

		if (response.getScripts() == null || response.getScripts().isEmpty()) {
			return null;
		}

		return response.getScripts().get(0);
	}

	// 删除脚本
	public void deleteScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName, String workspace) {
		DeleteScriptRequest request = new DeleteScriptRequest();
		request.setScriptName(scriptName);
		request.setWorkspace(workspace);
		DeleteScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).deleteScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("删除脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("删除脚本错误");
		}
	}


	// 执行脚本
	public String executeScript(String ak, String sk, String projectId, List<String> endpoints, String scriptName,
								String workspace) {
		ExecuteScriptRequest request = new ExecuteScriptRequest();
		request.withWorkspace(workspace);
		request.withScriptName(scriptName);
		ExecuteScriptReq body = new ExecuteScriptReq();
		//body.withParams("{}");
		request.withBody(body);
		ExecuteScriptResponse response = this.buildDgcClient(ak, sk, projectId, endpoints).executeScript(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("执行脚本错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("执行脚本错误");
		}
		return response.getInstanceId();
	}

	// 查询脚本实例执行结果
	public Map<String, String> listScriptResults(String ak, String sk, String projectId, List<String> endpoints,
												 String scriptName, String workspace, String instanceId) {
		ListScriptResultsRequest request = new ListScriptResultsRequest();
		request.withWorkspace(workspace);
		request.withScriptName(scriptName);
		request.withInstanceId(instanceId);
		ListScriptResultsResponse response = this.buildDgcClient(ak, sk, projectId, endpoints)
				.listScriptResults(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("查询脚本实例执行结果错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("查询脚本实例执行结果错误");
		}
		Map<String, String> result = new HashMap<>();
		result.put("status", response.getStatus());
		result.put("message", response.getMessage());
		return result;
	}

	//获取数据源中的表
	public List<TablesList> listTables(String ak, String sk, String projectId, List<String> endpoints, String connectionId,
									   String databaseName, String tableName) {
		ListDataTablesRequest request = new ListDataTablesRequest();
		request.withConnectionId(connectionId);
		request.withDatabaseName(databaseName);
		request.withTableName(connectionId);
		ListDataTablesResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints).listDataTables(request);
		if (!isOK(response.getHttpStatusCode())) {
			log.error("获取数据源中的表错误 httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取数据源中的表错误");
		}
		return response.getTables();
	}

	private boolean isOK(int status) {
		return status >= 200 && status < 300;
	}

	//获取表的字段
	public List<ColumnsList> listTableColumns(String ak, String sk, String projectId, List<String> endpoints, String connectionId, String workSpace,
											  String tableId) {
		ListColumnsRequest request = new ListColumnsRequest();
		request.withConnectionId(connectionId);
		request.withWorkspace(workSpace);
		request.withTableId(tableId);
		ListColumnsResponse response = this.buildDataArtsStudioClient(ak, sk, projectId, endpoints).listColumns(request);
		if (isOK(response.getHttpStatusCode())) {
			log.error("<获取表字段错误> httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("获取表的字段错误");
		}
		return response.getColumns();


	}

	/**
	 * 创建作业
	 *
	 * @param ak           ak
	 * @param sk           sk
	 * @param projectId    projectId
	 * @param endpoints    endpoints
	 * @param clusterId    集群 Id
	 * @param tables       迁移的表信息
	 * @param fromLinkName 源链接名称
	 * @param fromDatabase 源数据库
	 * @param toLinkName   目标链接名称
	 * @param toDatabase   目标数据库
	 */
	public CreateJobResponse createJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId,
									   List<CreateJobTableDTO> tables, String fromLinkName, String fromDatabase,
									   String toLinkName, String toDatabase
	) {
		List<Job> jobs = new ArrayList<>();

		for (CreateJobTableDTO table : tables) {
			var tableName = table.getTableName();
			var columnList = CollUtil.join(table.getColumnList(), "&");

			// 一、作业任务参数配置。例如配置作业失败重试、抽取并发数，具体可参考作业任务参数说明。
			List<Input> driverConfigsInputs = List.of(
					// 输入参数列表，列表中的每个参数为"name,value"结构，请参考inputs数据结构参数说明。
					new Input()
							.withName("groupJobConfig.groupName")
							.withValue("DEFAULT")
			);
			// 源连接参数、目的连接参数和作业任务参数，它们的配置数据结构相同，其中"inputs"里的参数不一样，详细请参见configs数据结构说明。
			List<Configs> driverConfigs = new ArrayList<>();
			driverConfigs.add(
					new Configs()
							.withInputs(driverConfigsInputs)
							.withName("groupJobConfig")
			);
			ConfigValues driverConfigValues = new ConfigValues();
			driverConfigValues.withConfigs(driverConfigs);

			// 二、源连接参数配置。根据不同源端有不同的参数配置，具体可参考源端作业参数说明下相应的源端参数配置。
			List<Input> fromConfigsInputs = List.of(
					new Input().withName("fromJobConfig.hive").withValue("hive"),
					new Input().withName("fromJobConfig.tableName").withValue(tableName),
					new Input().withName("fromJobConfig.columnList").withValue(columnList),
					new Input().withName("fromJobConfig.database").withValue(fromDatabase)
			);

			List<Configs> fromConfigs = new ArrayList<>();
			fromConfigs.add(new Configs().withInputs(fromConfigsInputs).withName("fromJobConfig"));
			ConfigValues fromConfigValues = new ConfigValues();
			fromConfigValues.withConfigs(fromConfigs);

			// 三、目的连接参数配置。根据不同目的端有不同的参数配置，具体可参考目的端作业参数说明下相应的目的端参数配置。
			List<Input> toConfigsInputs = List.of(
					new Input().withName("toJobConfig.database").withValue(toDatabase),
					new Input().withName("toJobConfig.table").withValue(tableName),
					new Input().withName("toJobConfig.tablePreparation").withValue("CREATE_WHEN_NOT_EXIST"),
					new Input().withName("toJobConfig.columnList").withValue(columnList),
					new Input().withName("toJobConfig.convertNull").withValue("TO_NULL"),
					new Input().withName("toJobConfig.shouldClearTable").withValue("false"),
					new Input().withName("toJobConfig.csvDelimPolicy").withValue("DROP"),
					new Input().withName("toJobConfig.clearDataMode").withValue("TRUNCATE")
			);
			List<Configs> toConfigs = new ArrayList<>();
			toConfigs.add(
					new Configs()
							.withInputs(toConfigsInputs)
							.withName("toJobConfig")
			);
			ConfigValues toConfigValues = new ConfigValues();
			toConfigValues.withConfigs(toConfigs);
			// 四、创建作业
			Job job = new Job()
					// 作业类型：NORMAL_JOB：表/文件迁移、BATCH_JOB：整库迁移、SCENARIO_JOB：场景迁移。
					.withJobType(Job.JobTypeEnum.fromValue("NORMAL_JOB"))
					// 源端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
					.withFromConnectorName("hive-connector")
					// 源连接参数配置
					.withFromConfigValues(fromConfigValues)
					// 源连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
					.withFromLinkName(fromLinkName) // bjsx-province-test
					// 目的端连接类型 generic-jdbc-connector：关系数据库连接 hive-connector：Hive连接 ...等
					.withToConnectorName("generic-jdbc-connector")
					// 目的连接参数配置
					.withToConfigValues(toConfigValues)
					// 目的端连接名称，即为通过"创建连接"接口创建的连接对应的连接名。
					.withToLinkName(toLinkName) // bsjx-hive-test
					// 作业任务参数配置
					.withDriverConfigValues(driverConfigValues)
					// 作业名称，长度在1到240个字符之间。
					.withName(tableName);

			jobs.add(job);
		}

		CreateJobRequest request = new CreateJobRequest();
		request.withClusterId(clusterId);
		CdmCreateJobJsonReq body = new CdmCreateJobJsonReq();

		body.withJobs(jobs);
		request.withBody(body);
		CreateJobResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).createJob(request);
		if (isOK(response.getHttpStatusCode())) {
			log.error("<创建作业失败> httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("创建作业失败");
		}

		return response;
	}

	public void startJob(String ak, String sk, String projectId, List<String> endpoints, String clusterId, String jobName) {
		StartJobRequest request = new StartJobRequest();
		request.setClusterId(clusterId);
		request.setJobName(jobName);
		StartJobResponse response = this.buildCdmClient(ak, sk, projectId, endpoints).startJob(request);
		if (isOK(response.getHttpStatusCode())) {
			log.error("<启动作业失败> httpStatusCode={}", response.getHttpStatusCode());
			throw new CheckedException("启动作业失败");
		}
	}
}
