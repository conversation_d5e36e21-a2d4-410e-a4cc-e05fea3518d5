package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.domain.model.aggregate.SysConnection;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareSourceDTO;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.time.LocalDateTime;

public interface ShareDataSourcesDomainService {
	void createShareDataSources(SysConnection sysConnection, Integer platformType, String appHandleCode, String items);

	PageResponse<ShareSourceDTO> listShareDataSources(ShareDataSourcesDomainEntity entity, LocalDateTime start, LocalDateTime end,
													  Integer current, Integer size);

	ShareDataSourcesDomainEntity findByAppHandleCode(String appHandleCode);
}
