package cn.teleinfo.ds.business.domain.service;

import cn.teleinfo.ds.business.application.command.sharetaskapplications.ShareTaskApplicationsCommand;
import cn.teleinfo.ds.business.application.query.HandleItemQuery;
import cn.teleinfo.ds.business.application.query.ShareTaskApplicationsQuery;
import cn.teleinfo.ds.business.domain.model.aggregate.ShareTaskApplicationDetails;
import cn.teleinfo.ds.business.domain.model.entity.AuthStatus;
import cn.teleinfo.ds.business.domain.model.entity.ShareTaskApplicationsDomainEntity;
import cn.teleinfo.ds.business.domain.model.valueobject.ShareTaskApplicationsId;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareTaskApplicationsView;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleApplicationResponse;
import cn.teleinfo.ds.business.interfaces.dto.response.HandleItemsResponse;
import com.pig4cloud.pig.common.core.util.PageResponse;

public interface ShareTaskApplicationsDomainService {


	/**
	 * 创建共享任务申请
	 */
	void createShareTaskApplications(ShareTaskApplicationsCommand command);

	/**
	 * 修改共享任务申请
	 */
	void updateShareTaskApplications(Long applicationId, ShareTaskApplicationsCommand command);

	/**
	 * 列表共享任务申请
	 */
	PageResponse<ShareTaskApplicationsView> listShareTaskApplications(ShareTaskApplicationsQuery query);


	/**
	 * 获取共享任务申请详情
	 */
	ShareTaskApplicationDetails getShareTaskApplicationDetail(Long applicationId);


	/**
	 * 查询一个共享任务申请的详情
	 *
	 * @param shareTaskApplicationsId 共享任务申请id
	 * @return 共享任务申请的详情
	 */
	ShareTaskApplicationsDomainEntity findShareTaskApplicationsDomainEntity(ShareTaskApplicationsId shareTaskApplicationsId);

	/**
	 * 更新共享任务申请状态
	 * @param id id
	 * @param authStatus 授权状态
	 */
	void updateApplicationsStatus(Long id, AuthStatus authStatus);

	/**
	 * 根据handle获取handleItems
	 * @param query handle
	 * @return handleItems
	 */
	HandleApplicationResponse getHandleItemsByHandle(HandleItemQuery query);

	void deleteShareTaskApplications(Long applicationId);
}
