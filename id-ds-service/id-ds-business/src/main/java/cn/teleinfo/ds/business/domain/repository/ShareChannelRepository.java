package cn.teleinfo.ds.business.domain.repository;

import cn.teleinfo.ds.business.application.query.ListShareChannelsQuery;
import cn.teleinfo.ds.business.domain.model.entity.DataChannelDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsDetailsDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.dto.ShareChannelsVersionDTO;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsDetailView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsApplicationsView;
import cn.teleinfo.ds.business.infrastructure.persistence.jpa.projection.ShareChannelsVersionSqlView;
import com.pig4cloud.pig.common.core.util.PageResponse;

import java.util.List;

public interface ShareChannelRepository {

	PageResponse<ShareChannelsDTO> listShareChannels(ListShareChannelsQuery query, Integer current, Integer size);

	ShareChannelsDetailsDTO queryShareChannelsDetails(String id);

	List<ShareChannelsDetailsDTO> listShareChannelsVersion(String shareChannelId);

	ShareChannelsVersionSqlView queryVersionSql(String shareChannelId, String main_version, String minor_version);

	List<ShareChannelsVersionDTO> queryShareChannelsVersionDTO(String shareChannelId);

	PageResponse<ShareChannelsApplicationsView> listShareChannelsApplications(ListShareChannelsQuery query,
			Integer current, Integer size);

	ShareChannelsApplicationsDetailView queryShareChannelsApplicationDetails(String applicationId);

	List<ShareChannel> findShareChannelsByDataChannelId(Long dataChannelId);

	void saveShareChannel(ShareChannel shareChannel);

	ShareChannel findEnableChannel(Long shareChannelId);

	DataChannelDomainEntity findDataChannel(Long shareChannelId);

	ShareChannel findMaxVersion(Long shareChannelId);

	void updateEnableByShareChannelIdAndVersion(String shareChannelId, Integer mainVersion, Integer minorVersion);

	List<String> queryChannelVersions(String shareChannelId);

	ShareChannel findByShareChannelIdAndVersion(Long shareChannelId, Integer mainVersion, Integer minorVersion);

	List<ShareChannel> findAllShareChannel();

	List<ShareChannel> findByShareChannelIdAndStatus(Long shareChannelId, Integer status);
}
