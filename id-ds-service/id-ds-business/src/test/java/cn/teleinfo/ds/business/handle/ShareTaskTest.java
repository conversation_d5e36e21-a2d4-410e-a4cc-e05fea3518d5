package cn.teleinfo.ds.business.handle;

import cn.hutool.json.JSONUtil;
import cn.teleinfo.ds.business.domain.model.aggregate.SharedTask;
import cn.teleinfo.ds.business.domain.model.entity.AppInfoDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.HandleItemDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.ShareChannel;
import cn.teleinfo.ds.business.domain.model.entity.ShareDataSourcesDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDetail;
import cn.teleinfo.ds.business.domain.model.entity.SharedTaskDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.SysConnectionDomainEntity;
import cn.teleinfo.ds.business.domain.model.entity.TargetSourceDomainEntity;
import cn.teleinfo.ds.business.domain.repository.HandlesRepository;
import cn.teleinfo.ds.business.domain.repository.ShareChannelRepository;
import cn.teleinfo.ds.business.domain.service.AppInfoDomainService;
import cn.teleinfo.ds.business.domain.service.HandlesDomainService;
import cn.teleinfo.ds.business.domain.service.ShareChannelsDomainService;
import cn.teleinfo.ds.business.domain.service.ShareDataSourcesDomainService;
import cn.teleinfo.ds.business.domain.service.SharedTaskDomainService;
import cn.teleinfo.ds.business.domain.service.SysConnectionDomainService;
import cn.teleinfo.ds.business.domain.service.TargetSourceDomainService;
import net.sf.jsqlparser.JSQLParserException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


@SpringBootTest
public class ShareTaskTest {
	@Autowired
	private SharedTaskDomainService sharedTaskDomainService;
	@Autowired
	private TargetSourceDomainService targetSourceDomainService;
	@Autowired
	private HandlesDomainService handlesDomainService;
	@Autowired
	private ShareDataSourcesDomainService shareDataSourcesDomainService;
	@Autowired
	private SysConnectionDomainService sysConnectionDomainService;
	@Autowired
	private AppInfoDomainService appInfoDomainService;
	@Autowired
	private ShareChannelsDomainService shareChannelsDomainService;

	private SharedTask sharedTask;

	@BeforeEach
	void initShareTask() {
		Long id = 1L;

		// 查询任务
		SharedTaskDomainEntity task = sharedTaskDomainService.getSharedTask(id);

		// 查询对象标识
		List<HandleDomainEntity> handles = new ArrayList<>();
		List<SharedTaskDetail> details = task.getDetails();
		for (SharedTaskDetail detail : details) {
			HandleDomainEntity handle = handlesDomainService.findByHandle(detail.getHandle());

			for (HandleItemDomainEntity item : handle.getHandleItems()) {
				ShareChannel enableChannel = shareChannelsDomainService.findEnableChannel(item.getDataChannelId());
				item.setShareChannel(enableChannel);
			}

			handles.add(handle);
		}


		// 查询应用
		AppInfoDomainEntity appInfo = appInfoDomainService.findByHandleCode(task.getAppHandleCode());

		// 查询共享源
		ShareDataSourcesDomainEntity shareDataSource = shareDataSourcesDomainService.findByAppHandleCode(task.getAppHandleCode());
		appInfo.setShareDataSources(shareDataSource);

		// 目标源
		TargetSourceDomainEntity targetSource = targetSourceDomainService.findById(task.getTargetSourceId());

		// 系统连接
		SysConnectionDomainEntity conn = sysConnectionDomainService.findByPlatformType(targetSource.getPlatformType());

		sharedTask = new SharedTask(task, handles, appInfo, targetSource, conn);
	}

	@Test
	void genOutputTablesSQLTest() {
		String SQL = sharedTaskDomainService.genOutputTablesSQL(sharedTask);

		System.out.println(SQL);

		System.out.println(JSONUtil.toJsonStr(sharedTask.getOutputTables()));
	}

	@Test
	void genExtractSQLTest() throws JSQLParserException {
		sharedTaskDomainService.genOutputTablesSQL(sharedTask);

		var SQL = sharedTaskDomainService.genExtractSQL(sharedTask);
		System.out.println();
		System.out.println(SQL);
		System.out.println();
	}

}
