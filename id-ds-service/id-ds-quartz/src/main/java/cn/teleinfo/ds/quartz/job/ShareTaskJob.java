package cn.teleinfo.ds.quartz.job;

import cn.hutool.core.util.NumberUtil;
import cn.teleinfo.ds.business.api.feign.RemoteShareTaskService;
import cn.teleinfo.ds.quartz.framework.quartz.core.handler.JobHandler;
import com.pig4cloud.pig.common.core.constant.SecurityConstants;
import com.pig4cloud.pig.common.core.util.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Slf4j
@Scope("prototype")
@Component
@AllArgsConstructor
public class ShareTaskJob implements JobHandler {
	private final RemoteShareTaskService remoteShareTaskService;

	/**
	 * 执行任务
	 *
	 * @param param 参数
	 * @return 结果
	 * @throws Exception 异常
	 */
	@Override
	public String execute(String param) throws Exception {
		if (!NumberUtil.isNumber(param)) {
			log.error("共享任务执行出错，参数非法 param={}", param);
			return "共享任务执行出错，参数非法";
		}

		R result = remoteShareTaskService.execute(Long.parseLong(param), SecurityConstants.FROM_IN);
		return result.getMessage();
	}
}
