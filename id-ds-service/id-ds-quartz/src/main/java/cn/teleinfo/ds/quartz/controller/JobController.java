package cn.teleinfo.ds.quartz.controller;


import cn.teleinfo.ds.quartz.vo.JobPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobRespVO;
import cn.teleinfo.ds.quartz.vo.JobSaveReqVO;
import cn.teleinfo.ds.quartz.entity.JobDO;
import com.pig4cloud.pig.common.core.util.R;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.common.util.object.BeanUtils;
import cn.teleinfo.ds.quartz.framework.quartz.core.util.CronUtils;
import cn.teleinfo.ds.quartz.service.JobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.quartz.SchedulerException;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Tag(name = "管理后台 - 定时任务")
@RestController
@RequestMapping("/job")
@Validated
public class JobController {

    @Resource
    private JobService jobService;

    @PostMapping("/create")
    @Operation(summary = "创建定时任务")
    public R<Long> createJob(@Valid @RequestBody JobSaveReqVO createReqVO) throws SchedulerException {
        return R.ok(jobService.createJob(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新定时任务")
    public R<Boolean> updateJob(@Valid @RequestBody JobSaveReqVO updateReqVO) throws SchedulerException {
        jobService.updateJob(updateReqVO);
        return R.ok(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新定时任务的状态")
    public R<Boolean> updateJobStatus(@RequestParam("id") Long id, @RequestParam("status") Integer status) throws SchedulerException {
        jobService.updateJobStatus(id, status);
        return R.ok(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除定时任务")
    @Parameter(name = "id", description = "编号", required = true)
    public R<Boolean> deleteJob(@RequestParam("id") Long id) throws SchedulerException {
        jobService.deleteJob(id);
        return R.ok(true);
    }

    @PutMapping("/trigger")
    @Operation(summary = "触发定时任务")
    public R<Boolean> triggerJob(@RequestParam("id") Long id) throws SchedulerException {
        jobService.triggerJob(id);
        return R.ok(true);
    }

    @PostMapping("/sync")
    @Operation(summary = "同步定时任务")
    public R<Boolean> syncJob() throws SchedulerException {
        jobService.syncJob();
        return R.ok(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得定时任务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public R<JobRespVO> getJob(@RequestParam("id") Long id) {
        JobDO job = jobService.getJob(id);
        return R.ok(BeanUtils.toBean(job, JobRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得定时任务分页")
    public R<PageResult<JobRespVO>> getJobPage(@Valid JobPageReqVO pageVO) {
		PageResult<JobDO> pageResult = jobService.getJobPage(pageVO);
        List<JobRespVO> respVOList = BeanUtils.toBean(pageResult.getList(), JobRespVO.class);
        // 设置下一次执行时间
        respVOList.forEach(this::fillJobRespVO);
        return R.ok(new PageResult<>(respVOList, pageResult.getTotal()));
    }

    private void fillJobRespVO(JobRespVO respVO) {
        if (!CronUtils.isValid(respVO.getCronExpression())) {
            return;
        }
        List<LocalDateTime> nextTimes = CronUtils.getNextTimes(respVO.getCronExpression(), 1);
        respVO.setNextTime(nextTimes.get(0));
    }


    @GetMapping("/get_next_times")
    @Operation(summary = "获得定时任务的下 n 次执行时间")
    @Parameters({
            @Parameter(name = "id", description = "编号", required = true, example = "1024"),
            @Parameter(name = "count", description = "数量", example = "5")
    })
    public R<List<LocalDateTime>> getJobNextTimes(
            @RequestParam("id") Long id,
            @RequestParam(value = "count", required = false, defaultValue = "5") Integer count) {
        JobDO job = jobService.getJob(id);
        if (job == null) {
            return R.ok(Collections.emptyList());
        }
        return R.ok(CronUtils.getNextTimes(job.getCronExpression(), count));
    }

} 