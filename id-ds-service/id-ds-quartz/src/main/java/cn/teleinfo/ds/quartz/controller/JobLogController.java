package cn.teleinfo.ds.quartz.controller;


import cn.teleinfo.ds.quartz.vo.JobLogPageReqVO;
import cn.teleinfo.ds.quartz.vo.JobLogRespVO;
import cn.teleinfo.ds.quartz.entity.JobLogDO;
import cn.teleinfo.ds.quartz.framework.common.pojo.PageResult;
import cn.teleinfo.ds.quartz.framework.common.util.object.BeanUtils;
import cn.teleinfo.ds.quartz.service.JobLogService;
import com.pig4cloud.pig.common.core.util.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 定时任务日志")
@RestController
@RequestMapping("/job/job-log")
@Validated
public class JobLogController {

    @Resource
    private JobLogService jobLogService;

    @GetMapping("/get")
    @Operation(summary = "获得定时任务日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public R<JobLogRespVO> getJobLog(@RequestParam("id") Long id) {
        JobLogDO jobLog = jobLogService.getJobLog(id);
        return R.ok(BeanUtils.toBean(jobLog, JobLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得定时任务日志分页")
    public R<PageResult<JobLogRespVO>> getJobLogPage(@Valid JobLogPageReqVO pageVO) {
		PageResult<JobLogDO> pageResult = jobLogService.getJobLogPage(pageVO);
        return R.ok(BeanUtils.toBean(pageResult, JobLogRespVO.class));
    }


} 